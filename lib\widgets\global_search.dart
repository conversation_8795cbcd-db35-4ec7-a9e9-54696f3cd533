import 'package:flutter/material.dart';
import 'package:medical/database/database_helper.dart';
import 'package:medical/models/models.dart';
import 'package:medical/services/navigation_service.dart';

class GlobalSearchDelegate extends SearchDelegate<String> {
  final DatabaseHelper _databaseHelper = DatabaseHelper();

  @override
  String get searchFieldLabel => 'Search family, appointments, medicines...';

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
          showSuggestions(context);
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () => close(context, ''),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults(context);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    if (query.isEmpty) {
      return _buildRecentSearches(context);
    }
    return _buildSearchResults(context);
  }

  Widget _buildSearchResults(BuildContext context) {
    return FutureBuilder<SearchResults>(
      future: _performSearch(query),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text('Error: ${snapshot.error}'),
          );
        }

        final results = snapshot.data!;
        final hasResults = results.familyMembers.isNotEmpty ||
            results.doctorVisits.isNotEmpty ||
            results.medicines.isNotEmpty ||
            results.diagnoses.isNotEmpty;

        if (!hasResults) {
          return _buildNoResults(context);
        }

        return ListView(
          children: [
            if (results.familyMembers.isNotEmpty) ...[
              _buildSectionHeader('Family Members'),
              ...results.familyMembers.map((member) => _buildFamilyMemberTile(context, member)),
            ],
            if (results.doctorVisits.isNotEmpty) ...[
              _buildSectionHeader('Doctor Visits'),
              ...results.doctorVisits.map((visit) => _buildDoctorVisitTile(context, visit)),
            ],
            if (results.medicines.isNotEmpty) ...[
              _buildSectionHeader('Medicines'),
              ...results.medicines.map((medicine) => _buildMedicineTile(context, medicine)),
            ],
            if (results.diagnoses.isNotEmpty) ...[
              _buildSectionHeader('Diagnoses'),
              ...results.diagnoses.map((diagnosis) => _buildDiagnosisTile(context, diagnosis)),
            ],
          ],
        );
      },
    );
  }

  Widget _buildRecentSearches(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Quick Actions',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        _buildQuickActionTile(
          context,
          'Add Family Member',
          Icons.person_add,
          () {
            close(context, '');
            NavigationService().addFamilyMember();
          },
        ),
        _buildQuickActionTile(
          context,
          'Schedule Appointment',
          Icons.calendar_today,
          () {
            close(context, '');
            _showAppointmentDialog(context);
          },
        ),
        _buildQuickActionTile(
          context,
          'Add Medicine',
          Icons.medication,
          () {
            close(context, '');
            _showMedicineDialog(context);
          },
        ),
        const Divider(),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            'Search Tips',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ),
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            '• Search by family member name\n'
            '• Search by doctor name\n'
            '• Search by medicine name\n'
            '• Search by diagnosis',
            style: TextStyle(fontSize: 14),
          ),
        ),
      ],
    );
  }

  Widget _buildNoResults(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'No results found for "$query"',
            style: TextStyle(
              fontSize: 18,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Try searching for family members, doctors, medicines, or diagnoses',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildFamilyMemberTile(BuildContext context, FamilyMember member) {
    return ListTile(
      leading: const CircleAvatar(
        child: Icon(Icons.person),
      ),
      title: Text(member.name),
      subtitle: Text('${member.relation} • Born: ${member.dateOfBirth}'),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        close(context, member.name);
        NavigationService().goToDoctorVisits(member);
      },
    );
  }

  Widget _buildDoctorVisitTile(BuildContext context, DoctorVisit visit) {
    return ListTile(
      leading: const CircleAvatar(
        child: Icon(Icons.local_hospital),
      ),
      title: Text(visit.doctorName),
      subtitle: Text('${visit.visitDate} • ${visit.reason}'),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () async {
        close(context, visit.doctorName);
        // Get family member for this visit
        final familyMember = await _databaseHelper.getFamilyMemberById(visit.familyMemberId);
        if (familyMember != null) {
          NavigationService().goToDoctorVisits(familyMember);
        }
      },
    );
  }

  Widget _buildMedicineTile(BuildContext context, Medicine medicine) {
    return ListTile(
      leading: const CircleAvatar(
        child: Icon(Icons.medication),
      ),
      title: Text(medicine.medicineName),
      subtitle: Text('${medicine.dosage} • ${medicine.frequency}'),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () async {
        close(context, medicine.medicineName);
        NavigationService().goToReminders(medicine);
      },
    );
  }

  Widget _buildDiagnosisTile(BuildContext context, Diagnosis diagnosis) {
    return ListTile(
      leading: const CircleAvatar(
        child: Icon(Icons.medical_services),
      ),
      title: Text(diagnosis.diagnosis),
      subtitle: Text('Date: ${diagnosis.date}'),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () async {
        close(context, diagnosis.diagnosis);
        // Navigate to the visit containing this diagnosis
        final visit = await _databaseHelper.getDoctorVisitById(diagnosis.visitId);
        if (visit != null) {
          NavigationService().goToDiagnoses(visit);
        }
      },
    );
  }

  Widget _buildQuickActionTile(BuildContext context, String title, IconData icon, VoidCallback onTap) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
      title: Text(title),
      trailing: const Icon(Icons.add, size: 16),
      onTap: onTap,
    );
  }

  Future<SearchResults> _performSearch(String query) async {
    if (query.isEmpty) {
      return SearchResults([], [], [], []);
    }

    final lowerQuery = query.toLowerCase();

    // Search family members
    final allFamilyMembers = await _databaseHelper.getFamilyMembers();
    final familyMembers = allFamilyMembers.where((member) =>
        member.name.toLowerCase().contains(lowerQuery) ||
        member.relation.toLowerCase().contains(lowerQuery)).toList();

    // Search doctor visits
    final allVisits = await _databaseHelper.getAllDoctorVisits();
    final doctorVisits = allVisits.where((visit) =>
        visit.doctorName.toLowerCase().contains(lowerQuery) ||
        visit.reason.toLowerCase().contains(lowerQuery) ||
        (visit.notes?.toLowerCase().contains(lowerQuery) ?? false)).toList();

    // Search medicines
    final allMedicines = await _databaseHelper.getAllMedicines();
    final medicines = allMedicines.where((medicine) =>
        medicine.medicineName.toLowerCase().contains(lowerQuery) ||
        medicine.dosage.toLowerCase().contains(lowerQuery)).toList();

    // Search diagnoses
    final allDiagnoses = await _databaseHelper.getAllDiagnoses();
    final diagnoses = allDiagnoses.where((diagnosis) =>
        diagnosis.diagnosis.toLowerCase().contains(lowerQuery)).toList();

    return SearchResults(familyMembers, doctorVisits, medicines, diagnoses);
  }

  void _showAppointmentDialog(BuildContext context) async {
    final familyMembers = await _databaseHelper.getFamilyMembers();

    if (familyMembers.isEmpty) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please add a family member first'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    if (context.mounted) {
      showDialog(
        context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Schedule Appointment'),
          content: const Text('Select a family member:'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ...familyMembers.map((member) => TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                NavigationService().addDoctorVisit(member.id!);
              },
              child: Text(member.name),
            )),
          ],
        );
      },
    );
    }
  }

  void _showMedicineDialog(BuildContext context) async {
    final visits = await _databaseHelper.getAllDoctorVisits();

    if (visits.isEmpty) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please add a doctor visit first'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    if (context.mounted) {
      showDialog(
        context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Add Medicine'),
          content: const Text('Select a doctor visit:'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ...visits.take(5).map((visit) => TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                NavigationService().addMedicine(visit.id!);
              },
              child: Text('${visit.doctorName} - ${visit.visitDate}'),
            )),
          ],
        );
      },
    );
  }
}

class SearchResults {
  final List<FamilyMember> familyMembers;
  final List<DoctorVisit> doctorVisits;
  final List<Medicine> medicines;
  final List<Diagnosis> diagnoses;

  SearchResults(this.familyMembers, this.doctorVisits, this.medicines, this.diagnoses);
}

// Global search button widget
class GlobalSearchButton extends StatelessWidget {
  const GlobalSearchButton({super.key});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.search),
      onPressed: () {
        showSearch(
          context: context,
          delegate: GlobalSearchDelegate(),
        );
      },
      tooltip: 'Search everything',
    );
  }
}
