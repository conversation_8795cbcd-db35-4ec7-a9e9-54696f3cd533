import 'package:flutter/material.dart';
import 'package:medical/models/models.dart';

class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  // Navigation breadcrumb tracking
  final List<NavigationItem> _navigationStack = [];
  
  List<NavigationItem> get navigationStack => List.unmodifiable(_navigationStack);
  
  // Quick access routes
  static const String dashboardRoute = '/dashboard';
  static const String familyRoute = '/family';
  static const String appointmentsRoute = '/appointments';
  static const String settingsRoute = '/settings';
  
  // Sub-routes
  static const String doctorVisitsRoute = '/doctor-visits';
  static const String diagnosesRoute = '/diagnoses';
  static const String medicinesRoute = '/medicines';
  static const String medicalReportsRoute = '/medical-reports';
  static const String remindersRoute = '/reminders';
  
  // Add routes
  static const String addFamilyMemberRoute = '/add-family-member';
  static const String addDoctorVisitRoute = '/add-doctor-visit';
  static const String addDiagnosisRoute = '/add-diagnosis';
  static const String addMedicineRoute = '/add-medicine';
  static const String addMedicalReportRoute = '/add-medical-report';
  static const String addReminderRoute = '/add-reminder';

  BuildContext? get context => navigatorKey.currentContext;

  // Push with breadcrumb tracking
  Future<T?> pushNamed<T extends Object?>(
    String routeName, {
    Object? arguments,
    String? title,
    IconData? icon,
  }) async {
    if (context == null) return null;
    
    _addToBreadcrumb(routeName, title, icon);
    return Navigator.of(context!).pushNamed<T>(routeName, arguments: arguments);
  }

  // Push replacement with breadcrumb update
  Future<T?> pushReplacementNamed<T extends Object?, TO extends Object?>(
    String routeName, {
    Object? arguments,
    String? title,
    IconData? icon,
    TO? result,
  }) async {
    if (context == null) return null;
    
    _replaceBreadcrumb(routeName, title, icon);
    return Navigator.of(context!).pushReplacementNamed<T, TO>(
      routeName, 
      arguments: arguments,
      result: result,
    );
  }

  // Pop with breadcrumb update
  void pop<T extends Object?>([T? result]) {
    if (context == null) return;
    
    _removeFromBreadcrumb();
    Navigator.of(context!).pop<T>(result);
  }

  // Pop until specific route
  void popUntil(String routeName) {
    if (context == null) return;
    
    Navigator.of(context!).popUntil((route) => route.settings.name == routeName);
    _updateBreadcrumbToRoute(routeName);
  }

  // Quick navigation methods
  void goToDashboard() {
    pushReplacementNamed(dashboardRoute, title: 'Dashboard', icon: Icons.dashboard);
  }

  void goToFamily() {
    pushReplacementNamed(familyRoute, title: 'Family', icon: Icons.people);
  }

  void goToAppointments() {
    pushReplacementNamed(appointmentsRoute, title: 'Appointments', icon: Icons.calendar_today);
  }

  void goToSettings() {
    pushReplacementNamed(settingsRoute, title: 'Settings', icon: Icons.settings);
  }

  // Context-aware navigation
  void goToDoctorVisits(FamilyMember familyMember) {
    pushNamed(
      doctorVisitsRoute,
      arguments: familyMember,
      title: '${familyMember.name} - Visits',
      icon: Icons.local_hospital,
    );
  }

  void goToDiagnoses(DoctorVisit visit) {
    pushNamed(
      diagnosesRoute,
      arguments: visit,
      title: 'Diagnoses - ${visit.visitDate}',
      icon: Icons.medical_services,
    );
  }

  void goToMedicines(DoctorVisit visit) {
    pushNamed(
      medicinesRoute,
      arguments: visit,
      title: 'Medicines - ${visit.visitDate}',
      icon: Icons.medication,
    );
  }

  void goToMedicalReports(DoctorVisit visit) {
    pushNamed(
      medicalReportsRoute,
      arguments: visit,
      title: 'Reports - ${visit.visitDate}',
      icon: Icons.receipt_long,
    );
  }

  void goToReminders(Medicine medicine) {
    pushNamed(
      remindersRoute,
      arguments: medicine,
      title: '${medicine.medicineName} - Reminders',
      icon: Icons.alarm,
    );
  }

  // Add/Edit navigation
  void addFamilyMember([FamilyMember? member]) {
    pushNamed(
      addFamilyMemberRoute,
      arguments: member,
      title: member == null ? 'Add Family Member' : 'Edit Family Member',
      icon: Icons.person_add,
    );
  }

  void addDoctorVisit(int familyMemberId, [DoctorVisit? visit]) {
    pushNamed(
      addDoctorVisitRoute,
      arguments: {'familyMemberId': familyMemberId, 'visit': visit},
      title: visit == null ? 'Add Doctor Visit' : 'Edit Doctor Visit',
      icon: Icons.add_circle,
    );
  }

  void addDiagnosis(int visitId, [Diagnosis? diagnosis]) {
    pushNamed(
      addDiagnosisRoute,
      arguments: {'visitId': visitId, 'diagnosis': diagnosis},
      title: diagnosis == null ? 'Add Diagnosis' : 'Edit Diagnosis',
      icon: Icons.add_circle,
    );
  }

  void addMedicine(int visitId, [Medicine? medicine]) {
    pushNamed(
      addMedicineRoute,
      arguments: {'visitId': visitId, 'medicine': medicine},
      title: medicine == null ? 'Add Medicine' : 'Edit Medicine',
      icon: Icons.add_circle,
    );
  }

  void addMedicalReport(int visitId, [MedicalReport? report]) {
    pushNamed(
      addMedicalReportRoute,
      arguments: {'visitId': visitId, 'report': report},
      title: report == null ? 'Add Medical Report' : 'Edit Medical Report',
      icon: Icons.add_circle,
    );
  }

  void addReminder(int medicineId, [Reminder? reminder]) {
    pushNamed(
      addReminderRoute,
      arguments: {'medicineId': medicineId, 'reminder': reminder},
      title: reminder == null ? 'Add Reminder' : 'Edit Reminder',
      icon: Icons.add_circle,
    );
  }

  // Breadcrumb management
  void _addToBreadcrumb(String route, String? title, IconData? icon) {
    _navigationStack.add(NavigationItem(
      route: route,
      title: title ?? _getDefaultTitle(route),
      icon: icon ?? _getDefaultIcon(route),
    ));
  }

  void _replaceBreadcrumb(String route, String? title, IconData? icon) {
    if (_navigationStack.isNotEmpty) {
      _navigationStack.removeLast();
    }
    _addToBreadcrumb(route, title, icon);
  }

  void _removeFromBreadcrumb() {
    if (_navigationStack.isNotEmpty) {
      _navigationStack.removeLast();
    }
  }

  void _updateBreadcrumbToRoute(String routeName) {
    final index = _navigationStack.indexWhere((item) => item.route == routeName);
    if (index != -1) {
      _navigationStack.removeRange(index + 1, _navigationStack.length);
    }
  }

  String _getDefaultTitle(String route) {
    switch (route) {
      case dashboardRoute: return 'Dashboard';
      case familyRoute: return 'Family';
      case appointmentsRoute: return 'Appointments';
      case settingsRoute: return 'Settings';
      case doctorVisitsRoute: return 'Doctor Visits';
      case diagnosesRoute: return 'Diagnoses';
      case medicinesRoute: return 'Medicines';
      case medicalReportsRoute: return 'Medical Reports';
      case remindersRoute: return 'Reminders';
      default: return 'Unknown';
    }
  }

  IconData _getDefaultIcon(String route) {
    switch (route) {
      case dashboardRoute: return Icons.dashboard;
      case familyRoute: return Icons.people;
      case appointmentsRoute: return Icons.calendar_today;
      case settingsRoute: return Icons.settings;
      case doctorVisitsRoute: return Icons.local_hospital;
      case diagnosesRoute: return Icons.medical_services;
      case medicinesRoute: return Icons.medication;
      case medicalReportsRoute: return Icons.receipt_long;
      case remindersRoute: return Icons.alarm;
      default: return Icons.help;
    }
  }

  // Clear navigation stack (useful for logout or reset)
  void clearNavigationStack() {
    _navigationStack.clear();
  }
}

class NavigationItem {
  final String route;
  final String title;
  final IconData icon;

  NavigationItem({
    required this.route,
    required this.title,
    required this.icon,
  });
}
