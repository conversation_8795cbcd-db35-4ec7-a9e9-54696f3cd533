import 'package:flutter/material.dart';
import 'package:medical/services/navigation_service.dart';
import 'package:medical/utils/responsive.dart';
import 'package:medical/database/database_helper.dart';

class QuickActionMenu extends StatefulWidget {
  final bool showLabels;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const QuickActionMenu({
    super.key,
    this.showLabels = true,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  State<QuickActionMenu> createState() => _QuickActionMenuState();
}

class _QuickActionMenuState extends State<QuickActionMenu>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  bool _isOpen = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleMenu() {
    setState(() {
      _isOpen = !_isOpen;
      if (_isOpen) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        // Backdrop to close menu when tapped
        if (_isOpen)
          GestureDetector(
            onTap: _toggleMenu,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent,
            ),
          ),
        
        // Quick action buttons
        ..._buildQuickActionButtons(context),
        
        // Main FAB
        FloatingActionButton(
          onPressed: _toggleMenu,
          backgroundColor: widget.backgroundColor ?? Theme.of(context).colorScheme.primary,
          foregroundColor: widget.foregroundColor ?? Theme.of(context).colorScheme.onPrimary,
          child: AnimatedRotation(
            turns: _isOpen ? 0.125 : 0.0, // 45 degree rotation
            duration: const Duration(milliseconds: 300),
            child: Icon(_isOpen ? Icons.close : Icons.add),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildQuickActionButtons(BuildContext context) {
    final actions = _getQuickActions(context);
    final List<Widget> buttons = [];

    for (int i = 0; i < actions.length; i++) {
      buttons.add(
        AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            final offset = (i + 1) * 70.0 * _animation.value;
            return Transform.translate(
              offset: Offset(0, -offset),
              child: Opacity(
                opacity: _animation.value,
                child: _buildActionButton(context, actions[i]),
              ),
            );
          },
        ),
      );
    }

    return buttons;
  }

  Widget _buildActionButton(BuildContext context, QuickAction action) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if (widget.showLabels && !Responsive.isMobile(context)) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                action.label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
            const SizedBox(width: 16),
          ],
          FloatingActionButton.small(
            onPressed: () {
              _toggleMenu();
              action.onPressed();
            },
            backgroundColor: action.backgroundColor ?? Theme.of(context).colorScheme.secondary,
            foregroundColor: action.foregroundColor ?? Theme.of(context).colorScheme.onSecondary,
            heroTag: action.heroTag,
            tooltip: action.label,
            child: Icon(action.icon),
          ),
        ],
      ),
    );
  }

  List<QuickAction> _getQuickActions(BuildContext context) {
    return [
      QuickAction(
        icon: Icons.person_add,
        label: 'Add Family Member',
        heroTag: 'add_family',
        backgroundColor: Colors.blue,
        onPressed: () => NavigationService().addFamilyMember(),
      ),
      QuickAction(
        icon: Icons.calendar_today,
        label: 'Schedule Appointment',
        heroTag: 'add_appointment',
        backgroundColor: Colors.green,
        onPressed: () => _showAppointmentDialog(context),
      ),
      QuickAction(
        icon: Icons.medication,
        label: 'Add Medicine',
        heroTag: 'add_medicine',
        backgroundColor: Colors.orange,
        onPressed: () => _showMedicineDialog(context),
      ),
      QuickAction(
        icon: Icons.receipt_long,
        label: 'Add Report',
        heroTag: 'add_report',
        backgroundColor: Colors.purple,
        onPressed: () => _showReportDialog(context),
      ),
    ];
  }

  void _showAppointmentDialog(BuildContext context) async {
    final familyMembers = await DatabaseHelper().getFamilyMembers();
    
    if (!mounted) return;
    
    if (familyMembers.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add a family member first'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Schedule Appointment'),
          content: const Text('Select a family member to schedule an appointment for:'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ...familyMembers.map((member) => TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                NavigationService().addDoctorVisit(member.id!);
              },
              child: Text(member.name),
            )),
          ],
        );
      },
    );
  }

  void _showMedicineDialog(BuildContext context) async {
    final visits = await DatabaseHelper().getAllDoctorVisits();
    
    if (!mounted) return;
    
    if (visits.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add a doctor visit first'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Add Medicine'),
          content: const Text('Select a doctor visit to add medicine to:'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ...visits.take(5).map((visit) => TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                NavigationService().addMedicine(visit.id!);
              },
              child: Text('${visit.doctorName} - ${visit.visitDate}'),
            )),
          ],
        );
      },
    );
  }

  void _showReportDialog(BuildContext context) async {
    final visits = await DatabaseHelper().getAllDoctorVisits();
    
    if (!mounted) return;
    
    if (visits.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please add a doctor visit first'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Add Medical Report'),
          content: const Text('Select a doctor visit to add report to:'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ...visits.take(5).map((visit) => TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                NavigationService().addMedicalReport(visit.id!);
              },
              child: Text('${visit.doctorName} - ${visit.visitDate}'),
            )),
          ],
        );
      },
    );
  }
}

class QuickAction {
  final IconData icon;
  final String label;
  final VoidCallback onPressed;
  final String heroTag;
  final Color? backgroundColor;
  final Color? foregroundColor;

  QuickAction({
    required this.icon,
    required this.label,
    required this.onPressed,
    required this.heroTag,
    this.backgroundColor,
    this.foregroundColor,
  });
}

// Simple quick access bar for tablets/desktop
class QuickAccessBar extends StatelessWidget {
  const QuickAccessBar({super.key});

  @override
  Widget build(BuildContext context) {
    if (Responsive.isMobile(context)) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            'Quick Actions:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          const SizedBox(width: 16),
          _buildQuickButton(
            context,
            'Add Family Member',
            Icons.person_add,
            () => NavigationService().addFamilyMember(),
          ),
          const SizedBox(width: 8),
          _buildQuickButton(
            context,
            'Schedule Appointment',
            Icons.calendar_today,
            () => _showAppointmentDialog(context),
          ),
          const SizedBox(width: 8),
          _buildQuickButton(
            context,
            'Add Medicine',
            Icons.medication,
            () => _showMedicineDialog(context),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickButton(BuildContext context, String label, IconData icon, VoidCallback onPressed) {
    return TextButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        textStyle: const TextStyle(fontSize: 12),
      ),
    );
  }

  void _showAppointmentDialog(BuildContext context) {
    // Implementation similar to QuickActionMenu
  }

  void _showMedicineDialog(BuildContext context) {
    // Implementation similar to QuickActionMenu
  }
}
