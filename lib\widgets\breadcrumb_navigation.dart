import 'package:flutter/material.dart';
import 'package:medical/services/navigation_service.dart';
import 'package:medical/utils/responsive.dart';

class BreadcrumbNavigation extends StatelessWidget {
  final bool showHomeButton;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? iconColor;

  const BreadcrumbNavigation({
    super.key,
    this.showHomeButton = true,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    final navigationService = NavigationService();
    final navigationStack = navigationService.navigationStack;

    if (navigationStack.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: Responsive.isMobile(context) ? 16.0 : 24.0,
        vertical: 8.0,
      ),
      decoration: BoxDecoration(
        color: backgroundColor ?? Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            if (showHomeButton) ...[
              _buildHomeButton(context),
              if (navigationStack.isNotEmpty) _buildSeparator(context),
            ],
            ..._buildBreadcrumbItems(context, navigationStack),
          ],
        ),
      ),
    );
  }

  Widget _buildHomeButton(BuildContext context) {
    return InkWell(
      onTap: () => NavigationService().goToDashboard(),
      borderRadius: BorderRadius.circular(4),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.home,
              size: 16,
              color: iconColor ?? Theme.of(context).colorScheme.primary,
            ),
            if (!Responsive.isMobile(context)) ...[
              const SizedBox(width: 4),
              Text(
                'Home',
                style: TextStyle(
                  fontSize: 14,
                  color: textColor ?? Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSeparator(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: Icon(
        Icons.chevron_right,
        size: 16,
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
      ),
    );
  }

  List<Widget> _buildBreadcrumbItems(BuildContext context, List<NavigationItem> navigationStack) {
    final List<Widget> items = [];
    
    for (int i = 0; i < navigationStack.length; i++) {
      final item = navigationStack[i];
      final isLast = i == navigationStack.length - 1;
      
      items.add(_buildBreadcrumbItem(context, item, isLast, i));
      
      if (!isLast) {
        items.add(_buildSeparator(context));
      }
    }
    
    return items;
  }

  Widget _buildBreadcrumbItem(BuildContext context, NavigationItem item, bool isLast, int index) {
    final isClickable = !isLast && index > 0; // Don't make the current page clickable
    
    Widget content = Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            item.icon,
            size: 16,
            color: isLast 
                ? (textColor ?? Theme.of(context).colorScheme.onSurface)
                : (iconColor ?? Theme.of(context).colorScheme.primary),
          ),
          if (!Responsive.isMobile(context) || isLast) ...[
            const SizedBox(width: 4),
            Text(
              _truncateTitle(item.title, context),
              style: TextStyle(
                fontSize: 14,
                color: isLast 
                    ? (textColor ?? Theme.of(context).colorScheme.onSurface)
                    : (textColor ?? Theme.of(context).colorScheme.primary),
                fontWeight: isLast ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );

    if (isClickable) {
      return InkWell(
        onTap: () => _navigateToItem(item, index),
        borderRadius: BorderRadius.circular(4),
        child: content,
      );
    }

    return content;
  }

  String _truncateTitle(String title, BuildContext context) {
    if (Responsive.isMobile(context) && title.length > 15) {
      return '${title.substring(0, 12)}...';
    } else if (Responsive.isTablet(context) && title.length > 25) {
      return '${title.substring(0, 22)}...';
    }
    return title;
  }

  void _navigateToItem(NavigationItem item, int index) {
    final navigationService = NavigationService();
    
    // Pop until we reach the desired item
    if (navigationService.context != null) {
      Navigator.of(navigationService.context!).popUntil((route) {
        return route.settings.name == item.route;
      });
      
      // Update the navigation stack to match
      navigationService.updateBreadcrumbToRoute(item.route);
    }
  }
}

// Extension to access private method (for breadcrumb navigation)
extension NavigationServiceExtension on NavigationService {
  void updateBreadcrumbToRoute(String routeName) {
    final index = navigationStack.indexWhere((item) => item.route == routeName);
    if (index != -1) {
      // This would need to be implemented in the NavigationService itself
      // For now, we'll handle this differently
    }
  }
}

// Quick access breadcrumb for specific contexts
class ContextualBreadcrumb extends StatelessWidget {
  final String currentTitle;
  final IconData currentIcon;
  final List<BreadcrumbAction> quickActions;

  const ContextualBreadcrumb({
    super.key,
    required this.currentTitle,
    required this.currentIcon,
    this.quickActions = const [],
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: Responsive.isMobile(context) ? 16.0 : 24.0,
        vertical: 12.0,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            currentIcon,
            size: 20,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              currentTitle,
              style: TextStyle(
                fontSize: Responsive.getSubtitleFontSize(context),
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
          if (quickActions.isNotEmpty) ...[
            const SizedBox(width: 16),
            ...quickActions.map((action) => Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: IconButton(
                onPressed: action.onPressed,
                icon: Icon(action.icon),
                tooltip: action.tooltip,
                iconSize: 20,
              ),
            )),
          ],
        ],
      ),
    );
  }
}

class BreadcrumbAction {
  final IconData icon;
  final VoidCallback onPressed;
  final String tooltip;

  BreadcrumbAction({
    required this.icon,
    required this.onPressed,
    required this.tooltip,
  });
}
